package customerApp.androidNative.homePage;

import base.BaseTest;
import com.google.common.collect.ImmutableMap;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RatingPopupTests extends BaseTest {

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping"), @Tag("rating")})
    public void verifyTippingSectionNotDisplayedWhenUserTippedOnCheckout() throws InterruptedException {
        // This test verifies that the tipping section is not displayed on the Rating screen
        // when the user has already tipped during checkout
        // Step 1: Launch the Breadfast Android app

        try {
            Thread.sleep(10000); // Wait 10 seconds for app to initialize
        } catch (InterruptedException e) {
            // Ignore interruption
        }

        // Check if we need to restart the app to get to a clean state
        if (!androidNativeHomeScreen.get().isPageDisplayed()) {
            //TODO: replace com.breadfast.testing to be dynamic incase we ran in other environments
            // Close the app
            androidDriver.get().terminateApp("com.breadfast.testing");
            Thread.sleep(2000); // Brief pause

            // Launch the app
            androidDriver.get().activateApp("com.breadfast.testing");

            Thread.sleep(5000); // Wait for app to restart
        }

        // Verify home screen is displayed
        boolean homeScreenDisplayed = androidNativeHomeScreen.get().isPageDisplayed();

        // Step 2: Add items to the cart
        // Navigate to a collection or category to add products
        androidNativeHomeScreen.get().pressFirstAvailableCollection();

        // Add first product to cart
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();

        // Navigate to cart
        androidNativeCollectionDetailsScreen.get().pressCartBtn();

        // Verify cart screen is displayed
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed(), "Cart screen is not displayed");

        // Step 3: Navigate to checkout
        androidNativeCartScreen.get().PressGoToCheckoutBtn();

        // Step 4: On the checkout screen, enter a specific tip amount (10 EGP)
        // Select a tip amount of 10 EGP
        androidCheckoutScreen.get().selectTipAmountElement(10);

        // Verify tip amount is displayed
        Assert.assertTrue(androidCheckoutScreen.get().isTipAmountDisplayed(),
                "Tip amount is not displayed on checkout screen");

        // Verify the tip value is 10 EGP
        Assert.assertEquals(androidCheckoutScreen.get().getTipValue(), 10.0,
                "Tip amount is not set to 10 EGP");

        // Step 5: Complete the order placement process
        // Select payment method (COD)
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Place the order
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        // Wait for order to be placed
        Thread.sleep(5000);

        // Step 6: Using the Control Room API, mark the order as delivered
        // Get the order ID from the test data
        String orderId = defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId();

        // Mark the order as delivered using the API
        // First mark as packed
        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser(), orderId, 3);

        // Mark as picked up
        orderApiClient.get().markOrderAsPickUpInRouteDelivering(defaultTestData.get().getAdminUser(),
                orderId, "pickup");

        // Mark as in-route
        orderApiClient.get().markOrderAsPickUpInRouteDelivering(defaultTestData.get().getAdminUser(),
                orderId, "in-route");

        // Mark as delivering
        orderApiClient.get().markOrderAsPickUpInRouteDelivering(defaultTestData.get().getAdminUser(),
                orderId, "delivering");

        // Mark as delivered
        orderApiClient.get().markOrderAsDelivered(defaultTestData.get().getAdminUser(),
                orderId, 10.0f, false);

        // Step 7: Relaunch the app
        // Close and relaunch the app using the driver's context
        androidDriver.get().terminateApp(androidDriver.get().getCurrentPackage());
        androidDriver.get().activateApp(androidDriver.get().getCurrentPackage());

        // Wait for the app to initialize properly after relaunch
        try {
            Thread.sleep(5000); // Wait 5 seconds for app to initialize
        } catch (InterruptedException e) {
            // Ignore interruption
        }

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Wait for rating popup to appear (up to 30 seconds)
        boolean ratingPopupDisplayed = false;
        for (int i = 0; i < 6; i++) {
            if (androidNativeRateOrderPopup.get().isRatingPopupDisplayed()) {
                ratingPopupDisplayed = true;
                break;
            }
            Thread.sleep(5000);
        }

        // Step 8: Rate the order by selecting a rating between 1-5 stars
        // Using 5 stars as an example
        androidNativeRateOrderPopup.get().rateOrderWithStars(5);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 9: Verify that the tipping section is NOT displayed
        // This is the main verification point of the test

        // Wait a moment for the rating UI to fully load
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            // Ignore
        }

        // Check if tipping section is displayed
        boolean tippingSectionDisplayed = androidNativeRateOrderPopup.get().isTippingSectionDisplayed();
        // Assert that the tipping section is NOT displayed
        Assert.assertFalse(tippingSectionDisplayed,
                "Tipping section is displayed even though the user already tipped during checkout");

        // Submit the rating
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkClosingRatingPopupOnHomeScreen() {
        // This test requires a recent delivered order to trigger the rating popup

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Check if rating popup is displayed
        // If it's displayed, close it and verify it's closed
        boolean isRatingPopupDisplayed = androidNativeRateOrderPopup.get().isRatingPopupDisplayed();

        // This assertion will fail the test if the rating popup is not displayed
        Assert.assertTrue(isRatingPopupDisplayed,
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");
        // 3. Press X icon of the Rating popup
        androidNativeRateOrderPopup.get().closeRatingPopup();

        // Expected result: Rating popup will be closed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(), "Rating popup is still displayed after closing");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkRatingOrderWithFiveStars() {
        // This test requires a recent delivered order to trigger the rating popup
        // Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");
        // Step 3: Rate the order with 5 stars
        // Expected result for step 3: Rate screen will be opened

        // Step 4: Check the UI of Rating screen
        // Expected results:
        // 1- 5 stars appears with green color
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // 2- Rating Name is Excellent
        Assert.assertTrue(androidNativeRateOrderPopup.get().isExcellentRatingTextDisplayed(),
                "Excellent rating text is not displayed");

        // Now rate the order with 5 stars
        androidNativeRateOrderPopup.get().rateOrderWithStars(5);

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Rating submission success message is not displayed");

        // Submit the rating
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after submitting rating");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkAddingCommentInRatingPopup() {
        // This test requires a recent delivered order to trigger the rating popup

        // Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");

        // Step 3: Rate the order with 5 stars
        // Expected result for step 3: Rating screen with 5 stars will be opened
        androidNativeRateOrderPopup.get().rateOrderWithStars(5);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isExcellentRatingTextDisplayed(),
                "Excellent rating text is not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isCommentTextFieldDisplayed(),
                "Comment text field is not displayed");

        // Step 4: In the field of 'Do you have a comment', enter any text
        // Expected result for step 4: user can add text in the field successfully
        String commentText = "This was an excellent delivery experience!";
        androidNativeRateOrderPopup.get().enterCommentInRatingPopup(commentText);

        // Step 5: Press Submit Rating
        // Expected result for step 5: Thank you for your Rating screen will be displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for your Rating screen is not displayed");

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after submitting rating");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkAddingTipInRatingPopup() {
        // This test requires a recent delivered order to trigger the rating popup
// Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");

        // Step 3: Rate the order with 5 stars
        // Expected result for step 3: Rating screen will be opened with selected rating stars
        androidNativeRateOrderPopup.get().rateOrderWithStars(5);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isExcellentRatingTextDisplayed(),
                "Excellent rating text is not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isCommentTextFieldDisplayed(),
                "Comment text field is not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isTippingSectionDisplayed(),
                "Tipping section is not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled");

        // Step 4: Select from the tipping amounts (10 EGP)
        // Expected result for step 4: Amount will be selected successfully
        androidNativeRateOrderPopup.get().selectTipAmount(10);

        // Step 5: If order with COD, make sure you have balance >=5 EGP
        // This is handled by the API setup

        // Step 6: Submit the rating
        // Expected result for step 6: Thank you screen will be displayed with tipping text
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Rating submission success message is not displayed");

        // Verify the tip success message is displayed
        Assert.assertTrue(androidNativeRateOrderPopup.get().isTipSuccessMessageDisplayed(),
                "Tip success message is not displayed");

        // Verify the tip success message contains the correct text
        String tipSuccessMessage = androidNativeRateOrderPopup.get().getTipSuccessMessageText();
        Assert.assertTrue(tipSuccessMessage.contains("A tip of") &&
                        tipSuccessMessage.contains("was sent to your delivery associate") &&
                        tipSuccessMessage.contains("Thank you for your generosity"),
                "Tip success message does not contain the expected text: " + tipSuccessMessage);

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after submitting rating");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkCustomTipFieldFunctionalityInRatingPopup() {
        // This test requires a recent delivered order to trigger the rating popup
// Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");
        // Step 3: Rate the order with any star rating (1-5)
        // Expected result for step 3: Rating screen will be opened with selected rating stars
        androidNativeRateOrderPopup.get().rateOrderWithStars(5);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isTippingSectionDisplayed(),
                "Tipping section is not displayed");

        // Step 4: In Tipping section, press custom field
        // Expected result for step 4: Input text field appears with Apply button
        androidNativeRateOrderPopup.get().clickCustomTipOption();
        Assert.assertTrue(androidNativeRateOrderPopup.get().isCustomTipInputFieldDisplayed(),
                "Custom tip input field is not displayed after clicking custom tip option");

        // Step 5: Enter a custom tip amount
        // Expected result for step 5: Amount is entered in the field
        String customTipAmount = "15";
        androidNativeRateOrderPopup.get().enterCustomTipAmount(customTipAmount);

        // Step 6: Press Apply button
        // Expected result for step 6: Custom field displays entered tip amount in the same UI style as default tips with an X icon
        androidNativeRateOrderPopup.get().clickApplyCustomTip();
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSelectedCustomTipAmountDisplayed(),
                "Selected custom tip amount is not displayed after applying");

        // Step 7: Press Submit Rating
        // Expected result for step 7: Thank you screen displays with text about the tip
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Rating submission success message is not displayed");

        // Verify the tip success message is displayed
        Assert.assertTrue(androidNativeRateOrderPopup.get().isTipSuccessMessageDisplayed(),
                "Tip success message is not displayed");

        // Verify the tip success message contains the correct text
        String tipSuccessMessage = androidNativeRateOrderPopup.get().getTipSuccessMessageText();
        Assert.assertTrue(tipSuccessMessage.contains("A tip of") &&
                        tipSuccessMessage.contains("was sent to your delivery associate") &&
                        tipSuccessMessage.contains("Thank you for your generosity"),
                "Tip success message does not contain the expected text: " + tipSuccessMessage);

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after submitting rating");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkThankYouForRatingScreenWithReferralEntryPoint() {
        // This test requires a recent delivered order to trigger the rating popup
// Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");
        // Step 3: Rate the order with 5 stars
        // Expected result for step 3: Rating screen with 5 stars Rating will be opened
        androidNativeRateOrderPopup.get().rateOrderWithStars(5);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Press Submit Rating
        // Expected result for step 4: Thank you for Rating screen will be displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Step 5: Check the Thank you for Rating screen UI
        // Verify the success message is displayed
        Assert.assertTrue(androidNativeRateOrderPopup.get().isThanksForRatingPopupDisplayed(),
                "Thank you for Rating popup is not displayed");

        // Step 6: Check that there is an entry point for referral
        // Expected result for step 6: Entry point for referral exists
        Assert.assertTrue(androidNativeRateOrderPopup.get().isReferralEntryPointDisplayed(),
                "Referral entry point is not displayed in the Thank you for Rating screen");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkProductQualitySelectionInRatingScreen() {
        // This test requires a recent delivered order to trigger the rating popup
// Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");

        // Step 3: Rate the order with any star rating (1-4)
        // Expected result for step 3: Rating screen opens with selected rating stars
        int starRating = 3; // Using 3 stars as an example (can be 1-4)
        androidNativeRateOrderPopup.get().rateOrderWithStars(starRating);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Select 'Product Quality'
        // Expected result for step 4: Section of order items will appear and user has to select an item
        androidNativeRateOrderPopup.get().selectProductQualityOption();

        // Verify that the concerned items section appears
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSelectConcernedItemsSectionDisplayed(),
                "Select concerned items section is not displayed after selecting Product Quality");

        // Verify that the Submit Rating button is disabled at this point
        Assert.assertFalse(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button should be disabled before selecting an item and entering quality issue");

        // Step 5: Select an item from 'select concerned Items'
        androidNativeRateOrderPopup.get().selectFirstConcernedItem();

        // Verify that the Quality Issue input text appears
        Assert.assertTrue(androidNativeRateOrderPopup.get().isQualityIssueInputFieldDisplayed(),
                "Quality Issue input field is not displayed after selecting a concerned item");

        // Verify that the Submit Rating button is still disabled
        Assert.assertFalse(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button should be disabled before entering quality issue text");

        // Step 6: Enter text in the Quality Issue input text
        // Expected result for step 6: Submit Rating button should be enabled
        String qualityIssueText = "The product was damaged during delivery";
        androidNativeRateOrderPopup.get().enterQualityIssueText(qualityIssueText);

        // Verify that the Submit Rating button is now enabled
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled after entering quality issue text");

        // Step 7: Press Submit Rating
        // Expected result for step 7: Thank you screen is displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkOtherReasonNotRequiredWithDeliveryAssociateOption() {
        // This test requires a recent delivered order to trigger the rating popup
        // Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating popup appears on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");
        // Step 3: Rate order with any star rating (1-4)
        // Expected result for step 3: Rating screen with the selected stars will be opened
        int starRating = 3; // Using 3 stars as an example (can be 1-4)
        androidNativeRateOrderPopup.get().rateOrderWithStars(starRating);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Select 'Delivery associate' option
        // Expected result for step 4: Submit rating button is enabled
        androidNativeRateOrderPopup.get().selectDeliveryAssociateOption();

        // Verify that the Submit Rating button is enabled
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled after selecting Delivery associate option");

        // Step 5: Leave other reason text field empty
        // Step 6: Press Submit rating button
        // Expected result for step 6: Thank you screen will be displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkOtherReasonNotRequiredWithPackagingOption() {
        // This test requires a recent delivered order to trigger the rating popup
        // Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating popup appears on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");
        // Step 3: Rate order with any star rating (1-4)
        // Expected result for step 3: Rating screen with the selected stars will be opened
        int starRating = 2; // Using 2 stars as an example (can be 1-4)
        androidNativeRateOrderPopup.get().rateOrderWithStars(starRating);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Select 'Packaging' option
        // Expected result for step 4: Submit rating button is enabled
        androidNativeRateOrderPopup.get().selectPackagingOption();

        // Verify that the Submit Rating button is enabled
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled after selecting Packaging option");

        // Step 5: Leave other reason text field empty
        // Step 6: Press Submit rating button
        // Expected result for step 6: Thank you screen will be displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkOtherReasonNotRequiredWithDeliveryTimeOption() {
        // This test requires a recent delivered order to trigger the rating popup
        // Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating popup appears on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");

        // Step 3: Rate order with any star rating (1-4)
        // Expected result for step 3: Rating screen with the selected stars will be opened
        int starRating = 4; // Using 4 stars as an example (can be 1-4)
        androidNativeRateOrderPopup.get().rateOrderWithStars(starRating);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Select 'Delivery Time' option
        // Expected result for step 4: Submit rating button is enabled
        androidNativeRateOrderPopup.get().selectDeliveryTimeOption();

        // Verify that the Submit Rating button is enabled
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled after selecting Delivery Time option");

        // Step 5: Leave other reason text field empty
        // Step 6: Press Submit rating button
        // Expected result for step 6: Thank you screen will be displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkCustomTipFieldWithLowRatingAndNoReferralEntryPoint() {
        // This test requires a recent delivered order to trigger the rating popup
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating popup appears on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");

        // Step 3: Rate the order with 3 stars (any rating between 1-4)
        // Expected result for step 3: Rating screen with 3 stars Rating will be opened
        androidNativeRateOrderPopup.get().rateOrderWithStars(3);

        // Verify the rating stars are displayed
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Select 'Delivery Time' option
        // Expected result for step 4: Custom tip input field appears with Apply button
        androidNativeRateOrderPopup.get().selectDeliveryTimeOption();

        // Verify that the Submit Rating button is enabled
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled after selecting Delivery Time option");

        // Step 5: Click on custom tip option
        androidNativeRateOrderPopup.get().clickCustomTipOption();

        // Verify custom tip input field is displayed
        Assert.assertTrue(androidNativeRateOrderPopup.get().isCustomTipInputFieldDisplayed(),
                "Custom tip input field is not displayed");

        // Step 6: Enter a valid tip amount in the custom field
        String tipAmount = "15";
        androidNativeRateOrderPopup.get().enterCustomTipAmount(tipAmount);

        // Step 7: Click Apply button
        androidNativeRateOrderPopup.get().clickApplyCustomTip();

        // Step 8: Press Submit Rating button
        // Expected result for step 8: Thank you for Rating screen will be displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Step 9: Verify the "Thank you for rating" screen appears
        Assert.assertTrue(androidNativeRateOrderPopup.get().isThanksForRatingPopupDisplayed(),
                "Thank you for Rating popup is not displayed");

        // Step 10: Confirm the referral entry point does NOT exist on this screen
        // Expected result for step 10: Referral entry point does not exist
        Assert.assertFalse(androidNativeRateOrderPopup.get().isReferralEntryPointDisplayed(),
                "Referral entry point is displayed in the Thank you for Rating screen when it should not be");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-41594"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkOtherReasonSelectionInRatingScreen() {
        // This test requires a recent delivered order to trigger the rating popup
// Register and login
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cod", // paymentMethod (cash on delivery)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Make sure you have a recent delivered order
        // Step 2: Open Breadfast app (you already logged in)
        // Expected result for step 2: Order Rating screen will appear on Home screen

        // Check if rating popup is displayed
        // If it's displayed, proceed with the test
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order. Test failed.");

        // Step 3: Rate the order with any star rating (1-4)
        // Expected result for step 3: Rating screen opens with selected rating stars
        int starRating = 3; // Using 3 stars as an example (can be 1-4)
        androidNativeRateOrderPopup.get().rateOrderWithStars(starRating);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");

        // Step 4: Select 'Other'
        // Expected result for step 4: Other reason input text will appear and it is required
        androidNativeRateOrderPopup.get().selectOtherReasonOption();

        // Verify that the Other reason input field appears
        Assert.assertTrue(androidNativeRateOrderPopup.get().isOtherReasonInputFieldDisplayed(),
                "Other reason input field is not displayed after selecting Other option");

        // Verify that the Submit Rating button is disabled at this point
        Assert.assertFalse(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button should be disabled before entering other reason text");

        // Step 5: Enter text in the Other reason input text
        // Expected result for step 5: Submit button will be enabled
        String otherReasonText = "The delivery was delayed by more than an hour";
        androidNativeRateOrderPopup.get().enterOtherReasonText(otherReasonText);

        // Verify that the Submit Rating button is now enabled
        Assert.assertTrue(androidNativeRateOrderPopup.get().isSubmitRatingButtonEnabled(),
                "Submit Rating button is not enabled after entering other reason text");

        // Step 6: Press Submit Rating
        // Expected result for step 6: Thank you screen is displayed
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Click the Done button to close the Thank you for Rating popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Verify the rating popup is no longer displayed
        Assert.assertFalse(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is still displayed after clicking Done button");
    }

    @Test(groups = {"CoreShoppingRegression", "B10-42666"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void checkCreditCardTipFunctionalityInRatingPopup() {
        // This test verifies that if the order is placed with CC, the same CC will appear
        // in the tipping section and the user can use it for tipping

        // Register and login
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create an order using API with credit card payment
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cc",  // paymentMethod (credit card)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Open Breadfast app and login
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed");

        // Step 1: Open Breadfast app (you are already logged in) → Rating popup appears on Home screen
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingPopupDisplayed(),
                "Rating popup is not displayed on home screen even after creating a delivered order with CC payment");

        // Step 2: Rate the order from 1 to 5 stars (this order is placed with cc) → Rating screen with the selected Rating stars will be opened
        androidNativeRateOrderPopup.get().rateOrderWithStars(4);

        // Verify the rating UI elements
        Assert.assertTrue(androidNativeRateOrderPopup.get().areRatingStarsDisplayed(),
                "Rating stars are not displayed");
        Assert.assertTrue(androidNativeRateOrderPopup.get().isTippingSectionDisplayed(),
                "Tipping section is not displayed");

        // Step 3: Check "pay with (same CC that order placed with)" exists in the tipping section
        Assert.assertTrue(androidNativeRateOrderPopup.get().isPayWithCreditCardOptionDisplayed(),
                "Pay with credit card option is not displayed in the tipping section");

        // Verify the credit card text contains expected information
        String creditCardText = androidNativeRateOrderPopup.get().getPayWithCreditCardText();
        Assert.assertTrue(creditCardText.contains("Pay with") && creditCardText.contains("ending in"),
                "Credit card payment option does not contain expected text: " + creditCardText);

        // Step 4: Select any default Tip amount
        androidNativeRateOrderPopup.get().selectTipAmount(10);

        // Step 5: Press the Submit Rating button → Enter CVV bottom sheet will appear
        androidNativeRateOrderPopup.get().submitRating();

        // Verify CVV bottom sheet appears
        Assert.assertTrue(androidNativeRateOrderPopup.get().isCvvBottomSheetDisplayed(),
                "CVV bottom sheet is not displayed after submitting rating with credit card tip");

        // Step 6: Enter the CVV and press Pay → Thank you screen will be displayed
        androidNativeRateOrderPopup.get().enterCvv("123");
        androidNativeRateOrderPopup.get().clickPayButton();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed");

        // Verify the tip success message is displayed and contains the correct text
        Assert.assertTrue(androidNativeRateOrderPopup.get().isTipSuccessMessageDisplayed(),
                "Tip success message is not displayed");

        String tipSuccessMessage = androidNativeRateOrderPopup.get().getTipSuccessMessageText();
        Assert.assertTrue(tipSuccessMessage.contains("A tip of") &&
                        tipSuccessMessage.contains("was sent to your delivery associate") &&
                        tipSuccessMessage.contains("Thank you for your generosity"),
                "Tip success message does not contain the expected text: " + tipSuccessMessage);

        // Close the thank you popup
        androidNativeRateOrderPopup.get().clickTheDoneButtonInThanksForRatingPopup();

        // Step 7: Rate screen from 1 to 5 stars (second rating attempt)
        // Create another order to trigger rating popup again
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",  // deliveryFees
                        "0",   // tippingAmount
                        "cc",  // paymentMethod (credit card)
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, // useBalance
                        false, // isScheduled
                        false, // isScheduledExpress
                        false  // giftReceipt
                )
        );

        // Complete the order cycle to mark it as delivered
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 20.0f, false);

        // Relaunch the app to trigger new rating popup
        androidDriver.get().terminateApp(androidDriver.get().getCurrentPackage());
        androidDriver.get().activateApp(androidDriver.get().getCurrentPackage());

        // Wait for the app to initialize properly after relaunch
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            // Ignore interruption
        }

        // Verify home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed(), "Home screen is not displayed after relaunch");

        // Wait for rating popup to appear
        boolean ratingPopupDisplayed = false;
        for (int i = 0; i < 6; i++) {
            if (androidNativeRateOrderPopup.get().isRatingPopupDisplayed()) {
                ratingPopupDisplayed = true;
                break;
            }
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                // Ignore interruption
            }
        }

        Assert.assertTrue(ratingPopupDisplayed, "Rating popup is not displayed for the second order");

        // Rate the second order
        androidNativeRateOrderPopup.get().rateOrderWithStars(3);

        // Step 8: Check that the tipping section does not exist → Tipping section does not exist
        Assert.assertFalse(androidNativeRateOrderPopup.get().isTippingSectionDisplayed(),
                "Tipping section is displayed even though user already tipped in the previous rating");

        // Submit the rating without tip
        androidNativeRateOrderPopup.get().submitRating();

        // Verify the rating was submitted successfully
        Assert.assertTrue(androidNativeRateOrderPopup.get().isRatingSubmittedSuccessfully(),
                "Thank you for Rating screen is not displayed for second rating");
    }
}
